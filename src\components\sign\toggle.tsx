"use client";

import SignIn from "./sign_in";
import User from "./user";
import { useAppContext } from "@/contexts/app";
import { isAuthEnabled } from "@/lib/auth";

export default function SignToggle() {
  const { user } = useAppContext();

  // Debug logging
  console.log('SignToggle render:', {
    isAuthEnabled: isAuthEnabled(),
    authEnabled: process.env.NEXT_PUBLIC_AUTH_ENABLED,
    googleEnabled: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED,
    githubEnabled: process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED,
    oneTapEnabled: process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED,
    user: !!user
  });

  // Temporary fix: force show auth for production
  const shouldShowAuth = isAuthEnabled() || (typeof window !== 'undefined' && window.location.hostname === 'bratgenerator.casa');

  if (!shouldShowAuth) {
    return null;
  }

  return (
    <div className="flex items-center gap-x-2 px-2 cursor-pointer">
      {user ? <User user={user} /> : <SignIn />}
    </div>
  );
}
