export function isAuthEnabled(): boolean {
  // If explicitly disabled, return false
  if (process.env.NEXT_PUBLIC_AUTH_ENABLED === "false") {
    return false;
  }

  // If explicitly enabled or any auth provider is enabled, return true
  return !!(
    process.env.NEXT_PUBLIC_AUTH_ENABLED === "true" ||
    process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" ||
    process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" ||
    process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true"
  );
}

export function isGoogleAuthEnabled(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" &&
    process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
  );
}

export function isGitHubAuthEnabled(): boolean {
  return process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true";
}

export function isGoogleOneTapEnabled(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true" &&
    process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
  );
}
